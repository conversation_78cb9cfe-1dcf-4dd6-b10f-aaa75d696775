<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;

class CheckRequestPaymentBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$canRunJob = parent::canRunJob();
		if (!$canRunJob) {
			return 'job will for nextday';
		}
		
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtProcessing::query()
			->where('is_sent_request', 1)
			->where('checked_at', '<=', now())
			->select(['contract_code', 'partner_request_id'])
			->chunkById(self::CHUNK_LIMIT, function (Collection $listProcessing) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processBatch($client, $listProcessing);
			});
		return "Batch: $this->done done, $this->failed failed";
	}


	private function processBatch(Client $client, Collection $listProcessing)
	{
		$baseUrl = config('app.url');

		// Generator
		$requests = function () use ($listProcessing, $baseUrl) {
			foreach ($listProcessing as $processing) {
				$url = $baseUrl . '/HandleCheckMposRequest/' . $processing->partner_request_id;
				yield $processing->partner_request_id => new Request('POST', $url);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $partnerRequestId) {
				// $body = (string)$response->getBody();
				$this->done++;
			},
			'rejected' => function (\Throwable $reason, string $partnerRequestId) {
				$msg = "[CheckRequest --->$partnerRequestId] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->failed++;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		$this->closeProcessing($listProcessing);
	}

	public function closeProcessing(Collection $listProcessing)
	{
		$listIds = $listProcessing->pluck('partner_request_id')->toArray();

		return CollectDebtProcessing::query()->whereIn('partner_request_id', $listIds)->update([
			'checked_at' => now()->addMinutes(30)
		]);
	}

	public function HandleCheckMposRequest(string $partnerRequestId): string
	{
		$request = request();

		$collectDebtRequest = CollectDebtRequest::query()->with('collectDebtPartner')->firstWhere(['partner_request_id' => $partnerRequestId]);

		if (!$collectDebtRequest) {
			return 'Request NotFound';
		}

		if (!$collectDebtRequest->collectDebtPartner) {
			$collectDebtRequestChecked = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request);
			$collectDebtRequest->mpos_debt_result = $collectDebtRequestChecked;
		}

		// có partner rồi thì update timecheck
		if ($collectDebtRequest->collectDebtPartner) {
			$collectDebtRequest->forceFill(['time_checked' => time()])->update();
		}

		return $collectDebtRequest->partner_request_id;
	}
}  // End class