<?php

use Illuminate\Support\Facades\Route; 
use App\Modules\CollectDebt\Actions\CollectDebtEvent\PushEventBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\PushSapDenHanBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtEvent\CreateOverCycleOverdueEventAction;

/*-------------------Send event dạng batch--------------------*/
	Route::any('/PushEventBatchAction', function () {
		return app(PushEventBatchAction::class)->run();
	});

	Route::any('/HandleEvent/{id}', function ($id) {
		return app(PushEventBatchAction::class)->HandleEvent($id);
	});
/*-------------------./ End Send event dạng batch--------------------*/

/*-------------------Tạo event chậm kỳ, quá hạn--------------------*/
	Route::any('/CreateOverCycleOverdueEventAction', function () {
		return app(CreateOverCycleOverdueEventAction::class)->run();
	});

	Route::any('/HandleOverCycleOverdue/{id}', function ($id) {
		return app(CreateOverCycleOverdueEventAction::class)->HandleOverCycleOverdue($id);
	});
/*-------------------./End Tạo event chậm kỳ, quá hạn--------------------*/

/*-------------------Sắp đến kỳ, Sắp đến hạn--------------------*/
	Route::any('/SyncContractNearCycleNearDue', function () {
		return app(PushSapDenHanBatchAction::class)->SyncContractNearCycleNearDue();
	});

	Route::any('/PushSapDenHanBatchAction', function () {
		return app(PushSapDenHanBatchAction::class)->run();
	});

	Route::any('/HandleReminder/{id}', function ($id) {
		return app(PushSapDenHanBatchAction::class)->HandleReminder($id);
	});
/*-------------------./End Tạo event chậm kỳ, quá hạn--------------------*/


