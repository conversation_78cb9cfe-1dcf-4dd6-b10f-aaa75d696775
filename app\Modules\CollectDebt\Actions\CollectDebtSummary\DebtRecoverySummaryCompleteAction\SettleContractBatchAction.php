<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction;

use Exception;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use App\Lib\NextlendCore;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLog;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class SettleContractBatchAction
{
	private const CHUNK_LIMIT = 30;
	private const POOL_CONCURRENCY = 5;
	private const HTTP_TIMEOUT = 10;
	private const BATCH_LIMIT_IN_SECONDS = 60; // quá trình chunks rồi batch chỉ trong 60s mà thôi

	private array $__processedIds = [];
	private array $__errorIds = [];

	public NextlendCore $nextlendCore;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->nextlendCore = $nextlendCore;
	}

	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtSummary::query()
			->where('status_contract', CollectDebtEnum::SUMMARY_STATUS_CONTRACT_CHUA_TAT_TOAN)
			->where('is_fully_paid', 1)
			->chunkById(self::CHUNK_LIMIT, function (Collection $listSummary) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processBatchSettle($client, $listSummary);
			});

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}

	private function createHttpClient(): Client
	{
		return new Client([
			'base_uri' => config('app.url'),
			'timeout' => self::HTTP_TIMEOUT,
			'verify' => false
		]);
	}

	private function processBatchSettle(Client $client, Collection $collectDebtSummaries): void
	{
		// Generator
		$requests = function () use ($collectDebtSummaries) {
			foreach ($collectDebtSummaries as $summary) {
				$url = config('app.url') . '/HandleSettleContract/' . $summary->contract_code;
				yield $summary->contract_code => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $contractCode) {
				// $body = (string)$response->getBody();
				Log::info("[TatToan --->$contractCode] success");
				$this->__processedIds[] = $contractCode;
			},
			'rejected' => function (\Throwable $reason, string $contractCode) {
				$msg = "[TatToan --->$contractCode] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function handleSettleContract(string $contractCode): string
	{
		$collectDebtSummary = CollectDebtSummary::query()
			->where('contract_code', $contractCode)
			->whereColumn('total_amount_paid', '>=', 'contract_amount')
			->whereRaw('fee_overdue_paid + fee_overdue_reduction >= fee_overdue')
			->whereRaw('fee_overdue_cycle_reduction + fee_overdue_cycle_paid >= fee_overdue_cycle')
			->first();

		throw_if(!$collectDebtSummary, new Exception('Contract not found or not enough peach to settle'));

		if ($collectDebtSummary->isHopDongTest()) {
			return $this->__markAsSettle($collectDebtSummary);
		}

		$r = app(DebtRecoverySummaryCompleteAction::class)->run($collectDebtSummary);
		return $r->contract_code;
	}

	private function __markAsSettle(CollectDebtSummary $collectDebtSummary): string
	{

		CollectDebtSummary::query()->where('id', $collectDebtSummary->id)->update([
			'status_contract' => CollectDebtEnum::SUMMARY_STATUS_CONTRACT_DA_TAT_TOAN,
			'time_updated' => now()->timestamp
		]);

		return $collectDebtSummary->contract_code;
	}
} // End class
