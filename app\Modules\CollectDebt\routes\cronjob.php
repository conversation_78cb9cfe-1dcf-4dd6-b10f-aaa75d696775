<?php

use Illuminate\Support\Facades\Route; 
use App\Http\Middleware\EnableSettingMiddleware;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide\CollectDebtGuideController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger\CollectDebtLedgerController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CanhBaoTrichNgayController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary\CollectDebtSummaryController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger\CollectDebtLedgerReportController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule\CollectDebtUpcomingPlanController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary\CollectDebtSummaryRefundController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtWaitProcess\CollectDebtWaitProcessController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtContractEvent\CollectDebtContractEventController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowController;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction\LedgerRecordBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\AccountingLedgerBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\PartnerCheckBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SendRequestBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction\SettleContractBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\AlertStuckRequestAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\CutOffRequestBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction\SyncContractBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\CheckRequestPaymentBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\AccountingSummaryBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction\DongBoVeHeThongHopDongAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction\SendNotifyViaChannelsAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\CreateRequestAutoBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoverySendBaoCaoTrichNoTnexAction\DebtRecoverySendBaoCaoTrichNoTnexAction;

// CronJob: Tạo lịch thu hồi từ chỉ dẫn rồi bắn vào api /DebtRecoveryContractPlanCreate 
Route::any('/DebtRecoveryContractGuideCreatePlan', [
  'as' => 'DebtRecoveryContractGuideCreatePlanAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide\CollectDebtGuideController@createPlan'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAO_LICH_TU_CHI_DAN'));

/*------------------- CronJob tạo lệnh trích tự động từ lịch thu--------------------*/
	// Route::any('/DebtRecoveryContractPlanCreateRequest', [
	// 	'as' => 'DebtRecoveryContractPlanCreateRequestAction',
	// 	'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule\CollectDebtScheduleRequestController@createRequest'
	// ])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAO_YEU_CAU_TU_LICH'));

	Route::any('/CreateRequestAutoBatchAction', function () {
		return app(CreateRequestAutoBatchAction::class)->run();
	});

	Route::any('/HandleCreateRequestAuto/{contractCode}', function (string $contractCode) {
		return app(CreateRequestAutoBatchAction::class)->handleCreateRequestAuto($contractCode);
	});

/*------------------- ./End CronJob tạo lệnh trích tự động từ lịch thu--------------------*/

Route::any('/DebtRecoveryContractCreateRequestViaWallet', [
  'as' => 'DebtRecoveryContractCreateRequestViaWalletAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule\CollectDebtScheduleRequestController@createRequestViaWallet'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAO_YEU_CAU_TU_LICH'));

/*-------------------Gửi lệnh trích sang mpos--------------------*/
	// Route::any('/DebtRecoveryRequestSendPayment', [
	// 	'as' => 'DebtRecoveryRequestSendPaymentAction',
	// 	'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestController@sendPayment'
	// ])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_GUI_LENH_SANG_DOI_TAC'));

	Route::any('/SendRequestBatchAction', function () {
		return app(SendRequestBatchAction::class)->run();
	});

	Route::any('/HandleSendRequestMpos/{partnerRequestId}', function (string $partnerRequestId) {
		return app(SendRequestBatchAction::class)->HandleSendRequestMpos($partnerRequestId);
	});
/*-------------------./End Gửi lệnh trích sang mpos--------------------*/

/*------------------- CronJob kiểm tra kết quả trích nợ--------------------*/
	// Route::any('/DebtRecoveryRequestCheckPayment', [
	// 	'as' => 'DebtRecoveryRequestCheckPaymentAction',
	// 	'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestController@checkPayment'
	// ])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_KIEM_TRA_KET_QUA_TRICH_NO'));

	Route::any('/CheckRequestPaymentBatchAction', function () {
		return app(CheckRequestPaymentBatchAction::class)->run();
	});

	Route::any('/HandleCheckMposRequest/{partnerRequestId}', function (string $partnerRequestId) {
		return app(CheckRequestPaymentBatchAction::class)->HandleCheckMposRequest($partnerRequestId);
	});
/*------------------- ./ End CronJob kiểm tra kết quả trích nợ--------------------*/

// Cronjob thực hiện giảm trừ số tiền đóng băng và giảm số dư ví sau khi lệnh trích đã đc hạch toán
Route::any('/DebtRecoveryRequestOpenAndMinusFreeze', [
  'as' => 'DebtRecoveryRequestOpenAndMinusFreezeAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestController@DebtRecoveryRequestOpenAndMinusFreeze'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_GIAM_TRU_SO_DU_VI_VA_TIEN_DONG_BANG_TRONG_VI'));

/*------------------- Cronjob xử lý Partner tiền về --------------------*/
	// Route::any('/DebtRecoveryPartnerCheck', [
	// 	'as' => 'DebtRecoveryPartnerCheckImproveAction',
	// 	'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtPartner\CollectDebtPartnerController@partnerCheck'
	// ])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_KIEM_TRA_DOI_TAC'));

	Route::any('/PartnerCheckBatchAction', function () {
		return app(PartnerCheckBatchAction::class)->run();
	});

	Route::any('/HandlePartnerCheck/{id}', function ($id) {
		return app(PartnerCheckBatchAction::class)->HandlePartnerCheck($id);
	});
/*------------------- ./ End Cronjob xử lý Partner tiền về --------------------*/


/*------------------- Ghi sổ --------------------*/
	// Route::any('/DebtRecoveryRequestRecored', [
	// 	'as' => 'ThucHienCaiTienGhiSoAction',
	// 	'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestController@recored'
	// ])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_GHI_SO'));

	Route::any('/LedgerRecordBatchAction', function () {
		return app(LedgerRecordBatchAction::class)->run();
	});

	Route::any('/HandleLedgerRecord/{partnerRequestId}', function (string $partnerRequestId) {
		return app(LedgerRecordBatchAction::class)->HandleLedgerRecord($partnerRequestId);
	});
/*-------------------./ End ghi sổ --------------------*/

/*------------------- Cronjob xử lý CutOff Time --------------------*/
	// Route::any('/DebtRecoveryRequestFinishCutOffTime', [
	// 	'as' => 'DebtRecoveryRequestFinishCutOffTime',
	// 	'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestCutOffController@handlerCutOff'
	// ])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_CUT_OFF_TIME'));

	Route::any('/CutOffRequestBatchAction', function () {
		return app(CutOffRequestBatchAction::class)->run();
	});

	Route::any('/HandleCutOffRequest/{partnerRequestId}', function (string $partnerRequestId) {
		return app(CutOffRequestBatchAction::class)->HandleCutOffRequest($partnerRequestId);
	});
/*------------------- ./End Cronjob xử lý CutOff Time --------------------*/

/*------------------- Hạch toán --------------------*/
	// Route::any('/DebtRecoveryLedgerAccounting', [
	// 	'as' => 'HachToanYeuCauTrenSoAction',
	// 	'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger\CollectDebtLedgerController@accouting'
	// ])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_HACH_TOAN'));

	Route::any('/AccountingLedgerBatchAction', function () {
		return app(AccountingLedgerBatchAction::class)->run();
	});

	Route::any('/HandleAccoutingLedger/{id}', function ($id) {
		return app(AccountingLedgerBatchAction::class)->HandleAccoutingLedger($id);
	});
/*------------------- ./End Hạch toán --------------------*/

/*-------------------Đẩy bảng tổng hợp--------------------*/
	// Route::any('/DebtRecoveryLedgerAccountingSummary', [
	// 	'as' => 'DebtRecoveryLedgerAccountingSummary',
	// 	'uses' => CollectDebtLedgerController::class . '@accoutingSummary'
	// ])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_HACH_TOAN'));

	Route::any('/AccountingSummaryBatchAction', function () {
		return app(AccountingSummaryBatchAction::class)->run();
	});

	Route::any('/HandleAccoutingSummary/{id}', function ($id) {
		return app(AccountingSummaryBatchAction::class)->HandleAccoutingSummary($id);
	});
/*-------------------./Đẩy bảng tổng hợp--------------------*/


// Job xu ly mail sap toi ky
Route::any('/DebtRecoveryHandleFirstUpcomingPlan', [
  'as' => 'DebtRecoveryHandleFirstUpcomingPlanAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleFirstUpcomingPlan'
]);

Route::any('/DebtRecoveryHandleSapToiHan', [
  'as' => 'DebtRecoveryHandleSapToiHanAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleSapToiHan'
]);

// Đến kỳ thanh toán
Route::any('/DebtRecoveryHandleMailDenKyDungNgay', [
  'as' => 'DebtRecoveryHandleMailDenKyDungNgayAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleMailDenKyDungNgay'
]);

// Đến hạn tất toán
Route::any('/DebtRecoveryHandleDenHanTatToanDungNgay', [
  'as' => 'DebtRecoveryHandleDenHanTatToanDungNgayAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleDenHanTatToanDungNgay'
]);

// Job duyệt tự động chỉ dẫn
Route::any('/DebtRecoveryGuideAutoApproved', [
  'as' => 'DebtRecoveryGuideAutoApproved',
  'uses' => CollectDebtGuideController::class . '@autoAprroved'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_DUYET_CHI_DAN_TU_DONG'));


// Thực hiện xử lý yêu cầu điều chỉnh và mở lại lịch
Route::any('/DebtRecoveryRequestAdjustmentProcess', [
  'as' => 'DebtRecoveryRequestAdjustmentProcessAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequestAdjustment\CollectDebtRequestAdjustmentController@process'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_XU_LY_YEU_CAU_DIEU_CHINH'));


/*-------------------Tất toán hợp đồng-------------------*/
	// Route::any('/DebtRecoverySummaryComplete', [
	// 	'as' => 'DebtRecoverySummaryFinishContractAction',
	// 	'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary\CollectDebtSummaryController@complete'
	// ])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAT_TOAN_HOP_DONG'));

	Route::any('/SettleContractBatchAction', function () {
		return app(SettleContractBatchAction::class)->run();
	});

	Route::any('/HandleSettleContract/{contractCode}', function (string $contractCode) {
		return app(SettleContractBatchAction::class)->handleSettleContract($contractCode);
	});
/*-------------------./End Tất toán hợp đồng-------------------*/

// Thực hiện recheck yêu cầu bị cutoff, yc trích ngay
Route::any('/DebtRecoveryRequestRecheck', [
  'as' => 'DebtRecoveryRequestRecheckAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestRecheckController@handleRecheck'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_RECHECK_YEU_CAU'));

// Job turn on dong bo tong hop ve he thong hop dong
Route::any('/DebtRecoverySummaryTurnOnContractMustSyncAt23h', [
  'as' => 'DebtRecoverySummaryTurnOnContractMustSyncAt23hAction',
  'uses' => CollectDebtSummaryController::class . '@turnOnContractMustSyncAt23h'
]);

/*-------------------Đồng bộ kết quả trích nợ về service hợp đồng----------------- */
	Route::any('/DebtRecoverySummaryCheckLedgerExsist', [
		'as' => DongBoVeHeThongHopDongAction::class,
		'uses' => CollectDebtSummaryController::class . '@checkLedgerExsist'
	])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_DONG_BO_HANG_NGAY'));

	Route::any('/SyncContractBatchAction', function () {
		return app(SyncContractBatchAction::class)->run();
	});

	Route::any('/HandleSyncContract/{contractCode}', function (string $contractCode) {
		return app(SyncContractBatchAction::class)->handleSyncContract($contractCode);
	});
/*-------------------./End Đồng bộ kết quả trích nợ về service hợp đồng----------------- */

// danh dau huy yc mpos khi co tien ve tu IB_OFF hoac VA
Route::any('/DebtRecoveryWaitProcessHandle', [
  'as' => 'DebtRecoveryWaitProcessHandleAction',
  'uses' => CollectDebtWaitProcessController::class . '@processHandle'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_DONG_BO_HANG_NGAY'));

// Mail qua han 3 cap do
Route::any('/DebtRecoverySummaryHandleMailOverdue', [
  'as' => 'DebtRecoverySummaryMailOverDueAction',
  'uses' => CollectDebtSummaryController::class . '@handleMailOverdue'
]);

// Mail cham ky
Route::any('/DebtRecoverySummaryHandleMailOverCycle', [
  'as' => 'DebtRecoverySummaryMailOverCycleAction',
  'uses' => CollectDebtSummaryController::class . '@handleMailOverCycle'
]);

// Xoa cac ban ghi event cu
Route::any('/DebtRecoveryContractDeleteOldEvent', [
  'as' => 'DebtRecoveryContractDeleteOldEvent',
  'uses' => CollectDebtContractEventController::class . '@deleteOldEvent'
]);

// api thuc hien check hoan tien cho: luong thu thua & luong hoan phi
Route::any('/DebtRecoverySummaryCheckRefund', [
	'as' => 'DebtRecoverySummaryCheckRefundAction',
	'uses' => CollectDebtSummaryRefundController::class . '@DebtRecoverySummaryCheckRefund'
]);

// Job kiểm tra lệnh trích ngay, nếu trích ko đủ tiền sẽ báo về email
Route::any('/DebtRecoveryRequestCanhBaoTrichNgay', [
	'as' => 'DebtRecoveryRequestCanhBaoTrichNgayAction',
	'uses' => CanhBaoTrichNgayController::class . '@handler'
]);

// Job gửi mail thông báo về tình trạng lệnh trích ngay
Route::any('/DebtRecoveryJobNotifyDebtNow', [
	'as' => SendNotifyViaChannelsAction::class,
	'uses' => CollectDebtNotifyDebtNowController::class . '@jobNotifyDebtNow'
]);

// Job báo cáo giao dịch trích nợ về cho bên HĐ làm việc với Tnex
Route::any('/DebtRecoverySendBaoCaoTrichNoTnex', [
	'as' => DebtRecoverySendBaoCaoTrichNoTnexAction::class,
	'uses' => CollectDebtLedgerReportController::class . '@sendReportTrichNoTnex'
]);

// Cảnh báo tắc lệnh trích
Route::any('/AlertStuckRequestAction', function () {
	return app(AlertStuckRequestAction::class)->run();
});