<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction;

use Exception;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SubAction\SendRequestViaMposSubAction;
use App\Modules\CollectDebt\Model\CollectDebtPlan;

class SendRequestBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$canRunJob = parent::canRunJob();
		if (!$canRunJob) {
			return 'job will for nextday';
		}

		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtProcessing::query()
			->from('debt_recovery_processing as p FORCE INDEX(`send_request_idx`)')
			->where('is_sent_request', 0)
			->where('expired_at', '>=', now())
			->select(['contract_code', 'partner_request_id'])
			->chunkById(self::CHUNK_LIMIT, function (Collection $collectDebtRequests) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processBatch($client, $collectDebtRequests);
			});

		return "Batch: $this->done done, $this->failed failed";
	}

	private function processBatch(Client $client, Collection $collectDebtRequests): void
	{
		$this->__processedIds = [];

		$baseUrl = config('app.url');

		// Generator
		$requests = function () use ($collectDebtRequests, $baseUrl) {
			foreach ($collectDebtRequests as $rq) {
				$url = $baseUrl . '/HandleSendRequestMpos/' . $rq->partner_request_id;
				yield $rq->partner_request_id => new Request('POST', $url, []);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $partnerRequestId) {
				$this->done++;
				$this->__processedIds[] = $partnerRequestId;
			},
			'rejected' => function (\Throwable $reason, string $partnerRequestId) {
				$msg = "[SendRequest --->$partnerRequestId] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->failed++;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function HandleSendRequestMpos(string $partnerRequestId)
	{
		$collectDebtRequest = CollectDebtRequest::query()
			->where('partner_request_id', $partnerRequestId)
			->first();

		if (!$collectDebtRequest) {
			return 'Request NotFound';
		}

		if ($collectDebtRequest->payment_method_code != 'MPOS') {
			return $partnerRequestId;
		}

		if (!empty($collectDebtRequest->partner_transaction_id)) {
			$this->markAsSent($partnerRequestId);
			return $partnerRequestId;
		}

		if ($collectDebtRequest->isRequestDaGhiSo()) {
			$this->markAsSent($partnerRequestId);
			return $partnerRequestId;
		}

		if ($collectDebtRequest->time_expired < time()) {
			$this->markAsSent($partnerRequestId);
			return $partnerRequestId;
		}

		// Xử lý case nhiều HĐ
		$plans = CollectDebtPlan::query()
		if ($collectDebtRequest->isUnsentPayment()) {
			$now = now();

			$collectDebtRequest->load(['collectDebtShareOnly:contract_code,priority,partner_code']);

			if ($collectDebtRequest->collectDebtShareOnly->priority == 1 && $now->lt(today()->addMinutes(30))) {
				return $partnerRequestId;
			}

			$rq = app(SendRequestViaMposSubAction::class)->run($collectDebtRequest);
		}

		if (empty($rq->partner_transaction_id)) {
			throw new Exception('Khong co thong tin chung tu gui lenh sang mpos');
		}

		$this->markAsSent($partnerRequestId);

		return $partnerRequestId;
	}

	private function markAsSent(string $partnerRequestId)
	{
		$r = CollectDebtProcessing::query()
			->where('partner_request_id', $partnerRequestId)
			->update(['is_sent_request' => 1, 'updated_at' => now()]);

		return $r;
	}
}  // End class