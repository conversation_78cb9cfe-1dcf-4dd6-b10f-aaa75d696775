[2025-08-18 12:53:58] local.INFO: HandleCheckMposRequest/NL25081859080 {"request":{"api_request_id":"DEBT_08181253_BArhZr"}} 
[2025-08-18 12:53:59] local.INFO: partner-collect-debt-gateway/check-debt {"request":{"data":{"partner_request_id":"NL25081859080","partner_merchant_id":"141486955","amount_payment":1500000,"contract_code":"MPOS-2508151442105-L2","loan_original_amount":9800000,"deduction_per_day_amount":1000000,"loan_balance":9500000,"request_id":59080,"users_admin_id":"{\"id\":\"cronjob\",\"username\":\"cronjob\",\"mobile\":\"cronjob\"}","payment_channel":"MPOS","amount":1500000},"checksum":"19772d418837366cb723e21d025937c1","channel_code":null,"time_request":1755496438,"version":"1.0","api_request_id":null}} 
[2025-08-18 12:53:59] local.INFO: [checkDebt] ---->[NL25081859080] {"func":"checkDebt","inputRaw":{"nextlend_request_id":"NL25081859080","partner_code":"MPOS","merchantId":"141486955","lendingRequestId":"NL25081859080","debitAmount":1500000,"requestTime":"20250818125359","lendingId":"MPOS-2508151442105-L2","loanOriginalAmount":9800000,"deductionPerDayAmount":1000000,"loanBalance":9500000,"_prefix":""},"requestInput":{"Fnc":"checkDebt","Checksum":"a09775da1a31960f02d8e3253e26d902","EncData":"9JHE4tIcRjKdHmDQKlPWQTly8FQg44gCbDDGkJAbxsCRrjPNmzevLD1KEwAvsvXmlBhZju3xUDFCL6TAU523Osjs2Nr/KxYjC2i0G0kTZOMoKNA2G63HjjsUG+rKUzl9jGenWUeVygYH7PjsgzN+bG1Sk5Fw4gIB7ZOAue1EVyk4REHqNT+il3wrrCdYnVpWVk3kwtIoxie0/ZHtx11j87eF+OE7txIt7+ngXaZEYbW9AB0m64EpjI5WCdQgPzX3NH33Ht20NsnlIVeXBPd2rnkzk3dekEAbmNzZ86l7kAmQHjaqH/hzvk2owWFqn5X8QFawJV1aRW4WGrcmcr/j7GZ3thTw+QDms0e9ryKMke0YdxThtKvDqUqeJ0I5Z0g6gQOiW2xvBp81tuAGIkp45X+EwVJEXdO/QSNIDAbaG4W23UzbPZGugV8gD7fs5Hcp","ChannelCode":"NEXTLENDV4","Version":"1.0"},"Result":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":141486955,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081859080\\\",\\\"mposDebtId\\\":141494645,\\\"debtAmount\\\":1500000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"a23e258006b5e18e6194ed9d0c5ae4c0","Description":"success"},"ParseError":{"errorCode":1000,"data":"{\"data\":{\"debtStatus\":\"PENDING\",\"merchantId\":141486955,\"lendingRequestId\":\"NEXTLENDV4-NL25081859080\",\"mposDebtId\":141494645,\"debtAmount\":1500000},\"status\":true,\"error\":{\"code\":1000,\"message\":\"DO_SERVICE_SUCCESS\",\"messageEn\":\"\"}}"}} 
[2025-08-18 12:57:57] local.INFO: HandleCheckMposRequest/NL25081859080 {"request":{"api_request_id":"DEBT_08181257_s2jKzy"}} 
[2025-08-18 12:57:57] local.INFO: partner-collect-debt-gateway/check-debt {"request":{"data":{"partner_request_id":"NL25081859080","partner_merchant_id":"141486955","amount_payment":1500000,"contract_code":"MPOS-2508151442105-L2","loan_original_amount":9800000,"deduction_per_day_amount":1000000,"loan_balance":9500000,"request_id":59080,"users_admin_id":"{\"id\":\"cronjob\",\"username\":\"cronjob\",\"mobile\":\"cronjob\"}","payment_channel":"MPOS","amount":1500000},"checksum":"c098dabfa982ec510271141787d209c0","channel_code":null,"time_request":1755496677,"version":"1.0","api_request_id":null}} 
[2025-08-18 12:57:58] local.INFO: [checkDebt] ---->[NL25081859080] {"func":"checkDebt","inputRaw":{"nextlend_request_id":"NL25081859080","partner_code":"MPOS","merchantId":"141486955","lendingRequestId":"NL25081859080","debitAmount":1500000,"requestTime":"20250818125757","lendingId":"MPOS-2508151442105-L2","loanOriginalAmount":9800000,"deductionPerDayAmount":1000000,"loanBalance":9500000,"_prefix":""},"requestInput":{"Fnc":"checkDebt","Checksum":"822d402eb793b52f2ca2a642b029d563","EncData":"Hsumx77aKWSreB0iHpF2R5dDNSW2+HRLIm0NIsau2WATn8h3uDynfHbEgdHljTlVDbDwKXcuqTbn3nE4OLtee5jSHBk29LpMI+NTz6ixDZHTKlXXSWsyp2wzlIqIs4JAGFl1II0xoQ8QeHTch/eKv8wfWdE/n4cyKPG7Iulm7U6EBjsCdTVg3nomtQouhj7q0wkkVU1eCUrMhE+qn+8gqiTjRJ37ireMGtjmUvcm6hGpyqWRDdSsS278ojsvwc11/9/l6W+NiAmAXifsGMVkC9HflJ5tOc9elSXKYrAdehbKdUbCFtNCuTJ5tuF2G2z9+sL6TicKigMUWrnl5e5t39rOLgrgLaOX2PaD37KGjLLxCZCrXwieFhP/MwFwmKpjysv74MMEQ8IQky4znD+brbaUOtvIzOLzlcO5xLVFmkkR26abeIVvAmfihKBCcyZ1","ChannelCode":"NEXTLENDV4","Version":"1.0"},"Result":{"Fnc":"checkDebt","Version":"1.0","ChannelCode":"NEXTLENDV4","RespCode":"00","data":"{\"errorCode\":1000,\"data\":\"{\\\"data\\\":{\\\"debtStatus\\\":\\\"PENDING\\\",\\\"merchantId\\\":141486955,\\\"lendingRequestId\\\":\\\"NEXTLENDV4-NL25081859080\\\",\\\"mposDebtId\\\":141494645,\\\"debtAmount\\\":1500000},\\\"status\\\":true,\\\"error\\\":{\\\"code\\\":1000,\\\"message\\\":\\\"DO_SERVICE_SUCCESS\\\",\\\"messageEn\\\":\\\"\\\"}}\"}","total":0,"Checksum":"a23e258006b5e18e6194ed9d0c5ae4c0","Description":"success"},"ParseError":{"errorCode":1000,"data":"{\"data\":{\"debtStatus\":\"PENDING\",\"merchantId\":141486955,\"lendingRequestId\":\"NEXTLENDV4-NL25081859080\",\"mposDebtId\":141494645,\"debtAmount\":1500000},\"status\":true,\"error\":{\"code\":1000,\"message\":\"DO_SERVICE_SUCCESS\",\"messageEn\":\"\"}}"}} 
[2025-08-18 13:49:15] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181349_ZfbG4f"}} 
[2025-08-18 13:49:37] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181349_GZVxyx"}} 
[2025-08-18 13:53:24] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181353_3eqTKl"}} 
[2025-08-18 13:55:30] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181355_vYcDZr"}} 
[2025-08-18 13:55:52] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181355_3mbm09"}} 
[2025-08-18 13:57:01] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181357_BvLybk"}} 
[2025-08-18 13:57:13] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181357_zZ4lbM"}} 
[2025-08-18 13:57:44] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181357_5Ley76"}} 
[2025-08-18 13:58:36] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181358_DajJeb"}} 
[2025-08-18 13:58:36] local.ERROR: syntax error, unexpected ';', expecting ']' {"exception":"[object] (ParseError(code: 0): syntax error, unexpected ';', expecting ']' at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Actions\\CollectDebtRequest\\DebtRecoveryRequestCheckPaymentAction\\AlertStuckRequestAction.php:27)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon8\\\\www...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Modules\\\\Col...')
#2 [internal function]: spl_autoload_call('App\\\\Modules\\\\Col...')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(809): ReflectionClass->__construct('App\\\\Modules\\\\Col...')
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(691): Illuminate\\Container\\Container->build('App\\\\Modules\\\\Col...')
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(796): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Col...', Array, true)
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(637): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Col...', Array)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(781): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Col...', Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(119): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Col...', Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\
outes\\cronjob.php(299): app('App\\\\Modules\\\\Col...')
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(225): Illuminate\\Support\\ServiceProvider->{closure}()
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(199): Illuminate\\Routing\\Route->runCallable()
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#37 {main}
"} 
[2025-08-18 13:58:43] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181358_DIfHfw"}} 
[2025-08-18 13:59:16] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181359_Sbch5I"}} 
[2025-08-18 13:59:52] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181359_00Bl5A"}} 
[2025-08-18 13:59:53] local.INFO: AlertStuckRequestAction {"request":{"api_request_id":"DEBT_08181359_lHhtQe"}} 
