<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction;

use Exception;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use App\Lib\NextlendCore;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventCreateSendAction\Task\GetHopDongNhanTienTuNguonThuThuaTask;

class SyncContractBatchAction extends BatchProcessingAction
{
	public NextlendCore $nextlendCore;

	public function __construct(NextlendCore $nextlendCore)
	{
		$this->nextlendCore = $nextlendCore;
	}
	
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtSummary::query()
			->where('is_request_sync', CollectDebtEnum::SUMMARY_CO_DONG_BO)
			->chunkById(self::CHUNK_LIMIT, function (Collection $listSummary) use ($client, $startTime) {

				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false; 
				}

				$this->processBatch($client, $listSummary);
			});

		return "Batch: $this->done done, $this->failed failed";
	}

	private function processBatch(Client $client, Collection $collectDebtSummaries): void
	{
		// Generator
		$requests = function () use ($collectDebtSummaries) {
			foreach ($collectDebtSummaries as $summary) {
				$url = config('app.url') . '/HandleSyncContract/' . $summary->contract_code;
				yield $summary->contract_code => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $contractCode) {
				// $body = (string)$response->getBody();
				$this->done++;
			},
			'rejected' => function (\Throwable $reason, string $contractCode) {
				$msg = "[DongBo --->$contractCode] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->failed++;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function handleSyncContract(string $contractCode): string
	{
		$collectDebtSummary = CollectDebtSummary::query()->firstWhere(['contract_code' => $contractCode]);

		throw_if(!$collectDebtSummary, new Exception('Contract not found'));

		if ($collectDebtSummary->isHopDongTest()) {
			return $this->__markAsSynced($collectDebtSummary);
		}

		$calcTime = app(DongBoVeHeThongHopDongAction::class)->__caculateTime($collectDebtSummary);
		$collectDebtSummary->number_day_status_debt = $calcTime['number_day_status_debt'];
		$collectDebtSummary->next_payment_period = $calcTime['next_payment_period'];

		$input = $collectDebtSummary->toArray();
		
		// Xử lý dùng tiền thừa thanh toán cho HĐ khác
		$input['excess_handler'] = [
			'refund' => [
				'amount_refund' => $collectDebtSummary->total_amount_excess_refund
			],
			'repayment' => [
				'amount_repayment' => $collectDebtSummary->total_amount_repayment_debt,
				'list_contract' =>  []
			]
		];

		if ( !empty($collectDebtSummary->total_amount_repayment_debt) ) {
			$listHopDongNhanTien = app(GetHopDongNhanTienTuNguonThuThuaTask::class)->run($collectDebtSummary->contract_code);
			if (isset($listHopDongNhanTien['data']['listMaHopDongNhanTienCashIn'])) {
				$input['excess_handler']['repayment']['list_contract'] = $listHopDongNhanTien['data']['listMaHopDongNhanTienCashIn'];
			}
		}
		
		$nextlend = $this->nextlendCore->callRequest($input, 'ContractV4_summaryData', 'POST');
		$result = $nextlend->decryptData();
		
		if ( empty($result['contract_id']) ) {
			throw new Exception('Dong bo hop dong bi loi');
		}

		return $this->__markAsSynced($collectDebtSummary);
	}

	private function __markAsSynced(CollectDebtSummary $collectDebtSummary): string {
		
		CollectDebtSummary::query()->where('id', $collectDebtSummary->id)->update([
			'is_request_sync' => CollectDebtEnum::SUMMARY_KHONG_DONG_BO,
			'time_updated' => now()->timestamp,
			'time_complated' => now()->timestamp
		]);

		return $collectDebtSummary->contract_code;
	}
} // End class
