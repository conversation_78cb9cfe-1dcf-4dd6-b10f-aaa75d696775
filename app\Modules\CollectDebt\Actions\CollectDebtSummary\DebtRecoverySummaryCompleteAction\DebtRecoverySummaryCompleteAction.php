<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Support\Str;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction\SubAction\Task\LuuHopDongTatToanDigitalNotiTask;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction\SubAction\DanhDauHopDongVer1LaDaTatToanSubAction;

class DebtRecoverySummaryCompleteAction
{
  public function run(CollectDebtSummary $collectDebtSummary)
  {
		$phiQuaHan = $collectDebtSummary->fee_overdue;
		$phiQuaHanDaThanhToan = $collectDebtSummary->fee_overdue_paid;
		$phiQuaHanDuocGiam = $collectDebtSummary->fee_overdue_reduction;

		mylog([
			'phiQuaHan' => $phiQuaHan,
			'phiQuaHanDaThanhToan' => $phiQuaHanDaThanhToan,
			'phiQuaHanDuocGiam' => $phiQuaHanDuocGiam,
		]);

		if ( ($phiQuaHanDaThanhToan + $phiQuaHanDuocGiam) < $phiQuaHan) {
			mylog(['thong tin phi qh ko hop le' => 'khong cho tat toan']);
			throw new Exception('thong tin phi qh ko hop le');
		}

		$phiChamKy = $collectDebtSummary->fee_overdue_cycle;
		$phiChamKyDaThanhToan = $collectDebtSummary->fee_overdue_cycle_paid;
		$phiChamKyDuocGiam = $collectDebtSummary->fee_overdue_cycle_reduction;

		if ( ($phiChamKyDaThanhToan + $phiChamKyDuocGiam) < $phiChamKy) {
			mylog(['thong tin phi CHAM KY ko hop le' => 'khong cho tat toan']);
			throw new Exception('thong tin phi CHAM KY ko hop le');
		}

		// Dam bao du tinh thu hoi, cac lich da ve trang thai cuoi
		$countLichChuaHoanThanh = CollectDebtPlan::query()
																						 ->where('contract_code', $collectDebtSummary->contract_code)
																		 				 ->where('status', '!=', CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH)
																		 				 ->count();

    throw_if($countLichChuaHoanThanh > 0, new Exception('Các lịch của HĐ chưa về trạng thái cuối'));


		$countLedgerChuaHoanThanh = CollectDebtLedger::query()
																					 ->where('contract_code', $collectDebtSummary->contract_code)
																					 ->whereNotIn('status_summary', [
																							CollectDebtEnum::LEDGER_STT_ACTION_DA_CAP_NHAT,
																							CollectDebtEnum::LEDGER_STT_ACTION_KHONG_XU_LY
																					 ])
																					 ->count();
		
		throw_if($countLedgerChuaHoanThanh > 0, new Exception('Các sổ chưa xử lý xong summary'));

    if (Str::startsWith($collectDebtSummary->contract_code, 'TEST')) {
      $danhDauHopDongLaDaTatToan['contract_id'] = 1; // HĐ fake thì fake luôn
			// HĐ fake thì cũng gọi tất toán luôn
			app(LuuHopDongTatToanDigitalNotiTask::class)->run($collectDebtSummary);
    }else {
			mylog(['TAT_TOAN_HD' => $collectDebtSummary->contract_code]);
      $danhDauHopDongLaDaTatToan = app(DanhDauHopDongVer1LaDaTatToanSubAction::class)->run($collectDebtSummary);
			mylog(['danhDauHopDongLaDaTatToan' => $danhDauHopDongLaDaTatToan]);
    }

		$otherData = $collectDebtSummary->getSummaryOtherData();
		
    if (isset($danhDauHopDongLaDaTatToan['contract_id'])) {
      $otherData[] = [
        'type' => 'SETTLEMENT',
        'note' => 'Tất toán hợp đồng',
        'time_modified' => time(),
        'data' => [
          'settlement_by' => Helper::getCronJobUser(),
          'contract' => $danhDauHopDongLaDaTatToan
        ]
      ];

      $otherData = collect($otherData)->map(function ($od) {
        if ($od['type'] == 'PAYMENT_METHOD') {
          $od['data'] = collect($od['data'])->map(function ($pm) {
            $pm['closed'] = 'YES';
            return $pm;
          })->all();
        }

        return $od;
      })->all();

      $updateParams = [
        'other_data' => json_encode($otherData, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES),
        'status' =>  CollectDebtEnum::SUMMARY_STT_DA_HOAN_THANH,
        'status_contract' => CollectDebtEnum::SUMMARY_STATUS_CONTRACT_DA_TAT_TOAN,
				'is_request_sync' => CollectDebtEnum::SUMMARY_CO_DONG_BO,
				'time_updated' => now()->timestamp,
      ];

      if (empty($collectDebtSummary->time_settlement)) {
        $updateParams['time_settlement'] = time();
      }

      $collectDebtSummary->forceFill($updateParams)->update();

			// Gọi save bản ghi noti tất toán HĐ tại đây
			app(LuuHopDongTatToanDigitalNotiTask::class)->run($collectDebtSummary);
    }

    return $collectDebtSummary;
  }
}
