<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction;

use Exception;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask\XuLySoBaoMuonTask;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCreateAction\SubAction\GetLichThuByIdsSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\DigitalNotiTask\LuuLenhTrichCoTienDigitalNotiTask;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\XuLyReportTnexSubAction\SaveGiaoDichTnexSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\HachToanSoThuThieuSubAction\HachToanSoDoiLuongSubAction;
use App\Modules\CollectDebt\Model\CollectDebtSummary;

class AccountingLedgerBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtLedger::query()
			->where('status', CollectDebtEnum::LEDGER_STT_CHUA_XU_LY)
			->chunkById(self::CHUNK_LIMIT, function (Collection $listLedger) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processPartnerBatch($client, $listLedger);
			});

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}


	private function processPartnerBatch(Client $client, Collection $listLedger): void
	{
		// Generator
		$requests = function () use ($listLedger) {
			foreach ($listLedger as $collectDebtLedger) {
				$url = config('app.url') . '/HandleAccoutingLedger/' . $collectDebtLedger->id;
				yield $collectDebtLedger->id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, $id) {
				// $body = (string)$response->getBody();
				Log::info("[AccoutingLedger --->$id] success");
				$this->__processedIds[] = $id;
			},
			'rejected' => function (\Throwable $reason, string $id) {
				$msg = "[AccoutingLedger --->$id] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function HandleAccoutingLedger($id)
	{
		$collectDebtLedgerCurrent = CollectDebtLedger::query()->find($id);

		if (!$collectDebtLedgerCurrent) {
			return 'Ledger NotFound';
		}

		if ($collectDebtLedgerCurrent->status != CollectDebtEnum::LEDGER_STT_CHUA_XU_LY) {
			return 'Ledger is processing...';
		}

		$updatedLenDangXuLy = CollectDebtLedger::query()
																					 ->where('id', $collectDebtLedgerCurrent->id)
																					 ->where('status', CollectDebtEnum::LEDGER_STT_CHUA_XU_LY)
																					 ->update([
																						'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN
																					 ]);
		if (!$updatedLenDangXuLy) {
			throw new Exception('khong the cap nhat so thang DANG HACH TOAN');
		}

		$collectDebtLedger = CollectDebtLedger::find($collectDebtLedgerCurrent->id); // khong dung refresh cho nay

		if ($collectDebtLedger->status != CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN) {
			throw new Exception('du lieu chua o trang thai dang hach toan, tu choi xu ly');
		}

		if ($collectDebtLedgerCurrent->amount != $collectDebtLedger->amount) {

			$updateVeChuaXuLy = CollectDebtLedger::query()
																					 ->where(['id' => $collectDebtLedger->id, 'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN])
																					 ->update(['status' => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY]);

			throw new Exception('[LOI NGHIEM TRONG] - Amount ghi so dang bi sai thong tin');
		}


		DB::beginTransaction();
		try {
			// Xu ly neu la so bao muon
			if ($collectDebtLedger->isSoDoiTacBaoMuon() || $collectDebtLedger->isSoThuKhongCoLich()) {
				mylog(['[SO BAO MUON hoac SO THU KHONG CO LICH]' => 'YES']);
				$updatedSoBaoMuon = app(XuLySoBaoMuonTask::class)->run($collectDebtLedger);

				if (!$updatedSoBaoMuon) {
					mylog(['[LOI UPDATE SO BAO MUON]' => $updatedSoBaoMuon]);
					throw new Exception('[LOI UPDATE SO BAO MUON]');
				}

				app(SaveGiaoDichTnexSubAction::class)->run($collectDebtLedger);
				DB::commit();
				return $collectDebtLedger;
			}
			
			$collectDebtLedger = $collectDebtLedger->load('collectDebtRequest');

			$collectDebtLedger->schedules = $this->getLichThuCuaSo($collectDebtLedger);
			
			// Xu ly hach toan
			mylog(['
				[SO CAN HACH TOAN]' => 'YES',
				'BAN GHI SO' => $collectDebtLedger
			]);

			//app(LuuLenhTrichCoTienDigitalNotiTask::class)->run($collectDebtLedger);
			
			$ledgerDaHachToan =  app(HachToanSoDoiLuongSubAction::class)->run($collectDebtLedger);
			app(SaveGiaoDichTnexSubAction::class)->run($collectDebtLedger);


			DB::commit();
			return $ledgerDaHachToan;
		}catch(\Throwable $th) {
			mylog(['[LOI TRANSACTION]' => Helper::traceError($th)]);

			DB::rollBack();
			$updatedVeChuaXuLy = CollectDebtLedger::query()
																						->where(['id' => $collectDebtLedger->id, 'status' => CollectDebtEnum::LEDGER_STT_DANG_HACH_TOAN])
																						->update(['status' => CollectDebtEnum::LEDGER_STT_CHUA_XU_LY]);
			if (!$updatedVeChuaXuLy) {
				mylog(['[LOI ROLLBACK]' => Helper::traceError($th)]);
				throw new Exception('LOI ROLLBACK, khong the cap nhat ban ghi so ve CHUA XU LY');
			}

			throw $th;
		}
		
		return $id;
	}

	public function getLichThuCuaSo(CollectDebtLedger $collectDebtLedger): Collection
	{
		if (empty($collectDebtLedger->plan_ids)) {
			return Collection::make();
		}

		$plans = app(GetLichThuByIdsSubAction::class)->run($collectDebtLedger->plan_ids);

		return $plans;
	}
}  // End class