<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent;

use Exception;
use App\Lib\Helper;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Ultilities\ReminderUtil;
use App\Modules\CollectDebt\Model\CollectDebtReminder;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\EmailRemind\Model\CollectDebtContractEvent;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\ReplaceNoiDungBySummarySubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleSapToiHanAction\SubAction\TaoEventMailSapToiHanTatToanSubAction;
use App\Modules\EmailRemind\Actions\CollectDebtContractEvent\DebtRecoveryContractEventBuildContentAction\SubAction\GetTemplateMailSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction\ReplaceNoiDungEmaiToiHanSubAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryHandleFirstUpcomingPlanAction\SubAction\TaoEventBuildNoiDungMailSubAction;

class PushSapDenHanBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtReminder::query()
			->where('fired_at', '<=', now())
			->where('is_finish', 0)
			->select(['id', 'fired_at', 'is_finish'])
			->chunkById(self::CHUNK_LIMIT, function (Collection $collectDebtRequests) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processBatch($client, $collectDebtRequests);
			});
		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}

	private function processBatch(Client $client, Collection $listEvent): void
	{
		// Generator
		$requests = function () use ($listEvent) {
			foreach ($listEvent as $ev) {
				$url = config('app.url') . '/HandleReminder/' . $ev->id;
				yield $ev->id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, $id) {
				// $body = (string)$response->getBody();
				$this->__processedIds[] = $id;
			},
			'rejected' => function (\Throwable $reason, $id) {
				$msg = "[SendMailEvent --->$id] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	private function canFinishReminderNow(CollectDebtReminder $collectDebtReminder): bool {
		$now = now();
		return (
			$now->gt($collectDebtReminder->fired_at) && !$now->isSameDay($collectDebtReminder->fired_at)
		) || (
			$collectDebtReminder->type == 'SAP_DEN_KY' && $collectDebtReminder->plan_id == 0
		);
	}

	public function HandleReminder($id)
	{
		$collectDebtReminder = CollectDebtReminder::query()->find($id);
		$now = now();

		if (!$collectDebtReminder) {
			return 'Reminder not found';
		}

		if ( $this->canFinishReminderNow($collectDebtReminder) ) {
			$collectDebtReminder->is_finish = 1;
			$collectDebtReminder->save();
			return $collectDebtReminder->id;
		}

		$contractCode = $collectDebtReminder->contract_code;

		$collectDebtGuide = CollectDebtGuide::query()
			->with(['collectDebtSummary', 'collectDebtShare'])
			->firstWhere(['contract_code' => $contractCode]);

		// Kết thúc reminder nếu guide ko tồn tại hoặc HĐ đã tất toán
		if (!$collectDebtGuide || $collectDebtGuide->collectDebtSummary->isHopDongDaTatToan()) {
			$collectDebtReminder->is_finish = 1;
			$collectDebtReminder->sent_at = now();
			$collectDebtReminder->description = 'HĐ lỗi không có lịch thu hoặc sai thông tin ngày xử lý';
			$collectDebtReminder->save();
			return $collectDebtReminder->id;
		}

		// Get ra các template mail đã được caching
		$templateTrichNgay = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => $collectDebtGuide->time_end_as_date->isSameDay(now()) ? 'DEN_HAN_THANH_TOAN_HD_NGAY' 
																																														 : 'NOTIFY_CONTRACT_DUE',
			'customer_service_care_code' => 'MAIL',
		]);

		$templateTrichKy = app(GetTemplateMailSubAction::class)->run([
			'customer_category_care_code' => $collectDebtGuide->time_end_as_date->isSameDay(now()) ? 'DEN_HAN_THANH_TOAN_HD_KY' 
																																														 : 'NOTIFY_CONTRACT_DUE_PERIOD',
			
			'customer_service_care_code' => 'MAIL',
		]);


		// Xử lý sắp đến hạn
		if ($collectDebtReminder->type == 'SAP_DEN_HAN') {
			$collectDebtEvent = app(TaoEventMailSapToiHanTatToanSubAction::class)->createOnly(
				$collectDebtGuide->collectDebtShare,
				$collectDebtGuide->collectDebtSummary,
			);

			// Replace bien vao trong noi dung email
			$emailContent = '';

			if ($collectDebtGuide->isChiDanHopDongTrichNgay()) {
				throw_if(empty($templateTrichNgay['content']), new \Exception("Contract: `$contractCode` has empty template content"));

				$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
					$collectDebtEvent,
					$collectDebtGuide->collectDebtSummary,
					$templateTrichNgay
				);
			}

			if ($collectDebtGuide->isChiDanHopDongTrichKy()) {
				throw_if(empty($templateTrichKy['content']), new \Exception("Contract: `$contractCode` has empty template content"));

				$emailContent = app(ReplaceNoiDungBySummarySubAction::class)->run(
					$collectDebtEvent,
					$collectDebtGuide->collectDebtSummary,
					$templateTrichKy
				);
			}

			throw_if(empty($emailContent), new \Exception("Contract: `$contractCode` has empty email content"));

			$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
			$collectDebtEvent->content = $emailContent;
			$savedResult = $collectDebtEvent->save();

			throw_if(!$savedResult, new \Exception("Can not saved mail content for: EventId: $collectDebtEvent->id -- ContractCode: $contractCode"));

			$collectDebtReminder->is_finish = 1;
			$collectDebtReminder->sent_at = now();
			$collectDebtReminder->save();

			return $contractCode;
		}

		// Xu ly sap den ky & den ky
		if ($collectDebtReminder->type == 'SAP_DEN_KY') {
			$plan = CollectDebtSchedule::query()->with(['collectDebtShare', 'collectDebtSummary'])->find($collectDebtReminder->plan_id);
			
			$template = app(GetTemplateMailSubAction::class)->run([
				'customer_category_care_code' => $plan->time_start_as_date->isSameDay(now()) ? 'DEN_KY_THANH_TOAN' : 'NOTIFY_CONTRACT_DUE_PERIOD',
				'customer_service_care_code' => 'MAIL',
			]);
			throw_if(empty($template['content']), new \Exception("Contract: `$contractCode` has empty template content"));
			
			$collectDebtEvent = app(TaoEventBuildNoiDungMailSubAction::class)->run($plan);
			throw_if(!$collectDebtEvent, new Exception('Loi khong tao duoc event'));
				
			$mailContent = app(ReplaceNoiDungEmaiToiHanSubAction::class)->run($plan, $collectDebtEvent, $template);
			throw_if(empty($mailContent), new Exception('Loi khong tao duoc noi dung mail'));

			$collectDebtEvent->status = CollectDebtEnum::EVENT_STT_DA_TAO_NOI_DUNG_MAIL;
			$collectDebtEvent->content = $mailContent;
			$savedResult = $collectDebtEvent->save();
			
			throw_if(!$savedResult, new \Exception("Can not saved mail content for: EventId: $collectDebtEvent->id -- ContractCode: $contractCode"));

			$collectDebtReminder->is_finish = 1;
			$collectDebtReminder->sent_at = now();
			$collectDebtReminder->save();

			return $contractCode;
		}

		return $id;
	}

	public function SyncContractNearCycleNearDue()
	{
		CollectDebtGuide::query()
			->where('contract_time_end', '>', now()->timestamp)
			->select(['contract_code', 'contract_time_start', 'contract_time_end', 'contract_type'])
			->chunkById(10, function (Collection $listGuide) {
				foreach ($listGuide as $guide) {
					$dates = ReminderUtil::getListDateSapDenHan($guide->time_end_as_date);

					foreach ($dates as $d) {
						CollectDebtReminder::query()->firstOrCreate([
							'contract_code' => $guide->contract_code,
							'fired_at' => $d,
							'type' => 'SAP_DEN_HAN',
						], [
							'channel' => 'EMAIL',
							'contract_code' => $guide->contract_code,
							'fired_at' => $d,
							'type' => 'SAP_DEN_HAN',
							'description' => 'Reminder xử lý HĐ sắp đến hạn'
						]);
					}
				}
			});

		return "ok";
	}
}  // End class