<?php

namespace App\Modules\CollectDebt\Ultilities;

use Exception;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtGuide;
use App\Modules\CollectDebt\Model\CollectDebtReminder;

class ReminderUtil
{
	// Liệt kê đúng các ngày: cách ngày đến hạn mốc: 5, 3, 2, 1, 0
	public static function getListDateSapDenHan(Carbon $contractTimeEnd)
	{
		$timeEnd = $contractTimeEnd->copy()->startOfDay();
		$dates = collect([5, 3, 2, 1, 0])->map(function ($days) use ($timeEnd) {
			return $timeEnd->copy()->subDays($days);
		});
		return $dates;
	}

	public static function createDateSapDenHan(CollectDebtGuide $collectDebtGuide, bool $isHopDongKy = false)
	{
		$dates = self::getListDateSapDenHan($collectDebtGuide->time_end_as_date);

		if (!$isHopDongKy) {
			$inputReminder = [];

			foreach ($dates as $d) {
				$inputReminder[] = [
					'channel' => 'EMAIL',
					'contract_code' => $collectDebtGuide->contract_code,
					'fired_at' => $d,
					'type' => 'SAP_DEN_HAN',
					'description' => 'Reminder xử lý HĐ sắp đến hạn'
				];
			}

			$reminder = CollectDebtReminder::query()->insert($inputReminder);

			if (!$reminder) {
				throw new Exception('Lỗi không tạo được reminder');
			}
		}

		return $dates;
	}

	public static function createReminder(CollectDebtGuide $collectDebtGuide, Collection $schedules)
	{
		if ($collectDebtGuide->isChiDanHopDongTrichNgay()) {
			return self::createDateSapDenHan($collectDebtGuide);
		}

		$listSapDenHan = self::createDateSapDenHan($collectDebtGuide, true);

		// Build param sắp đến hạn - đến hạn đúng ngày
		$inputReminder = [];

		foreach ($listSapDenHan as $d) {
			$inputReminder[] = [
				'channel' => 'EMAIL',
				'contract_code' => $collectDebtGuide->contract_code,
				'fired_at' => $d,
				'type' => 'SAP_DEN_HAN',
				'description' => 'Reminder sắp đến hạn',
				'plan_id' => 0,
			];
		}

		// Build ra param sắp đến kỳ - đến kỳ đúng ngày
		foreach ($schedules as $plan) {

			// Không tính kỳ cuối cùng
			if ($plan->is_settlement == 1) {
				continue;
			}

			$listDateCachMoc = self::getListDateSapDenHan($plan->time_start_as_date);
			foreach ($listDateCachMoc as $d) {
				$inputReminder[] = [
					'channel' => 'EMAIL',
					'contract_code' => $collectDebtGuide->contract_code,
					'fired_at' => $d,
					'type' => 'SAP_DEN_KY',
					'description' => 'Reminder sắp đến kỳ',
					'plan_id' => $plan->id
				];
			}
		}

		$reminder = CollectDebtReminder::query()->insert($inputReminder);

		if (!$reminder) {
			throw new Exception('Lỗi không tạo được reminder');
		}

		return [];
	}
} // End class
