<?php

namespace App\Modules\CollectDebt\Actions;

use App\Lib\Helper;
use GuzzleHttp\Client;

class BatchProcessingAction
{
	public const CHUNK_LIMIT = 20;
	public const POOL_CONCURRENCY = 20; // trong cùng thời đ<PERSON>, tối đa <PERSON><PERSON> lý song song 10 request
	public const HTTP_TIMEOUT = 10;
	public const BATCH_LIMIT_IN_SECONDS = 60;

	public array $__processedIds = [];
	public array $__errorIds = [];

	public int $done = 0;
	public int $failed = 0;

	public function createHttpClient(): Client
	{
		return new Client([
			'base_uri' => config('app.url'),
			'timeout' => self::HTTP_TIMEOUT,
			'connect_timeout' => 5,
			'http_errors'     => false, 
			'verify'          => false,
			'headers'         => [
					'Connection' => 'keep-alive'
			],
		]);
	}

	public function canRunJob(bool $checkViaCutOffTime=true): bool
	{
		if (!$checkViaCutOffTime) {
			return true;
		}
		
		$now = now();
		$cutOffTime = today()->setTimeFromTimeString(Helper::getCutOffTime());
		

		if ($now->gte($cutOffTime)) {
			return false;
		}

		return true;
	}
} // End class
