<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;

class AlertStuckRequestAction extends BatchProcessingAction
{
	public function run()
	{
		$listStuck = CollectDebtProcessing::query()
			->where('expired_at', '<=', now()->startOfDay())
			->get(['contract_code', 'partner_request_id']);

		$lastFive = $listStuck->take(5);
		$count = $listStuck->count();
		$leftCount = $count - 5;

		if ($listStuck->isEmpty()) {
			return 'Everything is processed';
		}

		$msg = sprintf('[env: %s] - Hiện có :%s và %s lệnh trích nợ bị treo', config('app.env'), $lastFive->toJson(), $leftCount);
		return TelegramAlert::alertLenhTrichBiTreo($msg);
	}
}  // End class