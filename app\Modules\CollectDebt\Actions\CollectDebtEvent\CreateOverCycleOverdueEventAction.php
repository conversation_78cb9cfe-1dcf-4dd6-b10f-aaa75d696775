<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtEvent;

use Exception;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtSummary;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryMailOverDueAction\DebtRecoverySummaryMailOverDueAction;

class CreateOverCycleOverdueEventAction extends BatchProcessingAction
{
	private function processChunks($column, $value)
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtSummary::where($column, $value)
			->select(['id'])
			->chunkById(self::CHUNK_LIMIT, function ($listSummary) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}
				$this->processBatch($client, $listSummary);
			});
	}


	public function run()
	{
		$this->processChunks('is_send_mail_slow_cycle', CollectDebtEnum::SUMMARY_CO_GUI_MAIL_CHAM_KY);

		$this->processChunks('is_send_mail_overdue', CollectDebtEnum::SUMMARY_CO_GUI_MAIL_QUA_HAN);

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}

	private function processBatch(Client $client, Collection $listSummary): void
	{
		// Generator
		$requests = function () use ($listSummary) {
			foreach ($listSummary as $collectDebtSummary) {
				$url = config('app.url') . '/HandleOverCycleOverdue/' . $collectDebtSummary->id;
				yield $collectDebtSummary->id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, $id) {
				// $body = (string)$response->getBody();
				$this->__processedIds[] = $id;
			},
			'rejected' => function (\Throwable $reason, $id) {
				$msg = "[CreateEventChamKyQuaHan --->$id] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function HandleOverCycleOverdue($id)
	{
		$collectDebtSummary = CollectDebtSummary::query()->find($id);

		if (!$collectDebtSummary) {
			return 'Contract NotFound';
		}

		if ($collectDebtSummary->is_send_mail_slow_cycle == 0 && $collectDebtSummary->is_send_mail_overdue == 0) {
			return "Contract: `$collectDebtSummary->contract_code` doesnt have any action";
		}

		if ($collectDebtSummary->isHopDongDaTatToan()) {
			$collectDebtSummary->is_send_mail_slow_cycle = 0;
			$collectDebtSummary->is_send_mail_overdue = 0;
			$collectDebtSummary->save();
			return 'Contract is done';
		}

		// Cần tạo event mail quá hạn
		if ($collectDebtSummary->is_send_mail_overdue == CollectDebtEnum::SUMMARY_CO_GUI_MAIL_QUA_HAN) {
			return $this->handleOverdueEvent($collectDebtSummary);
		}

		// Cần tạo event mail chậm kỳ
		if ($collectDebtSummary->is_send_mail_slow_cycle == CollectDebtEnum::SUMMARY_CO_GUI_MAIL_CHAM_KY) {
			return $this->handleSlowCycleEvent($collectDebtSummary);
		}

		throw new Exception('Khong biet la loi gi va tao event nao');
	}

	/**
	 * Xử lý tạo event mail quá hạn
	 */
	private function handleOverdueEvent(CollectDebtSummary $collectDebtSummary)
	{
		$days = $collectDebtSummary->number_day_overdue;
		$code = '';

		if ($days >= 0 && $days < 5) {
			$code = 'NOTIFY_CONTRACT_DUE1';
		} else if ($days >= 5 && $days <= 10) {
			$code = 'NOTIFY_CONTRACT_DUE2';
		} else if ($days >= 11) {
			$code = 'NOTIFY_CONTRACT_DUE3';
		}

		$isCreatedEvent = app(DebtRecoverySummaryMailOverDueAction::class)->__createEvent($code, $collectDebtSummary);

		if (!$isCreatedEvent) {
			$msg = "[QuaHan ----> Created event: $code failed ---> $collectDebtSummary->contract_code]";
			throw new Exception($msg);
		}

		$collectDebtSummary->is_send_mail_overdue = 0;
		$collectDebtSummary->is_send_mail_slow_cycle = 0;

		$r = $collectDebtSummary->save();

		throw_if(!$r, new Exception('Loi cap nhat summary ve trang thai da tao event'));

		return $collectDebtSummary->contract_code;
	}

	/**
	 * Xử lý tạo event mail chậm kỳ
	 */
	private function handleSlowCycleEvent(CollectDebtSummary $collectDebtSummary)
	{
		$code = 'CONTRACT_OVERDUE_CYCLE';
		$isCreatedEvent = app(DebtRecoverySummaryMailOverDueAction::class)->__createEvent($code, $collectDebtSummary);

		if (!$isCreatedEvent) {
			$msg = "[ChamKy ----> Created event: $code failed ---> $collectDebtSummary->contract_code]";
			throw new Exception($msg);
		}

		$collectDebtSummary->is_send_mail_slow_cycle = 0;

		$r = $collectDebtSummary->save();

		throw_if(!$r, new Exception('Loi cap nhat summary ve trang thai da tao event'));

		return $collectDebtSummary->contract_code;
	}
}  // End class