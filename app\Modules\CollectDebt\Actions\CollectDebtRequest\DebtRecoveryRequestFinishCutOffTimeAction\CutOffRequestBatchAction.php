<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction;

use Exception;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\SubAction\XuLyYeuCauTrichQuaDoiTacSubAction;

class CutOffRequestBatchAction extends BatchProcessingAction
{
	public function run()
	{
		$startTime = microtime(true);

		$client = $this->createHttpClient();

		CollectDebtProcessing::query()
			->where('expired_at', '<', now())
			->chunkById(self::CHUNK_LIMIT, function (Collection $listProcessing) use ($client, $startTime) {
				if ((microtime(true) - $startTime) > self::BATCH_LIMIT_IN_SECONDS) {
					$this->__errorIds[] = "Times up";
					return false;
				}

				$this->processCutOffBatch($client, $listProcessing);
			});

		return [
			'processed_ids' => $this->__processedIds,
			'error_ids' => $this->__errorIds
		];
	}


	private function processCutOffBatch(Client $client, Collection $listProcessing): void
	{
		// Generator
		$requests = function () use ($listProcessing) {
			foreach ($listProcessing as $collectDebtProcessing) {
				$url = config('app.url') . '/HandleCutOffRequest/' . $collectDebtProcessing->partner_request_id;
				yield $collectDebtProcessing->partner_request_id => new Request('POST', $url, ['Content-Type' => 'application/json']);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => self::POOL_CONCURRENCY,
			'fulfilled' => function ($response, string $partnerRequestId) {
				// $body = (string)$response->getBody();
				Log::info("[CutOffRequest --->$partnerRequestId] success");
				$this->__processedIds[] = $partnerRequestId;
			},
			'rejected' => function (\Throwable $reason, string $partnerRequestId) {
				$msg = "[CutOffRequest --->$partnerRequestId] failed: " . $reason->getMessage();
				Log::info($msg);
				$this->__errorIds[] = $msg;
			},
		]);

		$promise = $pool->promise();
		$promise->wait();
	}

	public function HandleCutOffRequest(string $partnerRequestId): string
	{
		$collectDebtRequest = CollectDebtRequest::query()->with(['collectDebtPartner', 'collectDebtShare'])
																										 ->firstWhere(['partner_request_id' => $partnerRequestId]);

		if (!$collectDebtRequest) {
			return 'Request NotFound';
		}

		if ($collectDebtRequest->time_expired > now()->timestamp) {
			return 'wrong time cutoff, deny request';
		}

		if ($collectDebtRequest->payment_method_code != 'MPOS') {
			return 'wrong payment method code';
		}

		if ($collectDebtRequest->isYeuCauCutOff()) {
			$this->__updateVeDaXuLyCutOff($collectDebtRequest);
			return $partnerRequestId;
		}

		if ($collectDebtRequest->collectDebtPartner) {
			$this->__updateVeDaXuLyCutOff($collectDebtRequest);
			return $partnerRequestId;
		}

		DB::beginTransaction();
		try {
			// Chưa có partner || không có mã chứng từ
			$coreContract = $collectDebtRequest->collectDebtShare->getCoreContractByGuide();

			$rq = app(XuLyYeuCauTrichQuaDoiTacSubAction::class)->run($collectDebtRequest, $coreContract);

			$this->__updateVeDaXuLyCutOff($collectDebtRequest);

			DB::commit();

			return $partnerRequestId;
		} catch (\Throwable $th) {
			DB::rollBack();
			throw new Exception($th->getMessage());
		}

		return $partnerRequestId;
	}

	private function __updateVeDaXuLyCutOff(CollectDebtRequest $collectDebtRequest)
	{
		return CollectDebtRequest::query()
			->where('id', $collectDebtRequest->id)
			->update([
				'status_cutoff' => CollectDebtEnum::REQUEST_DA_XL_CUT_OFF,
				'time_updated' => now()->timestamp
			]);
	}
}  // End class